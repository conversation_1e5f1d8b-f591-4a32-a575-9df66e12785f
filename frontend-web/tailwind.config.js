/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // Extend Tailwind with Quasar-compatible colors and spacing
      colors: {
        // Quasar primary colors
        primary: {
          50: '#e3f2fd',
          100: '#bbdefb',
          200: '#90caf9',
          300: '#64b5f6',
          400: '#42a5f5',
          500: '#2196f3', // Default Quasar primary
          600: '#1e88e5',
          700: '#1976d2',
          800: '#1565c0',
          900: '#0d47a1',
        },
        secondary: {
          50: '#fce4ec',
          100: '#f8bbd9',
          200: '#f48fb1',
          300: '#f06292',
          400: '#ec407a',
          500: '#e91e63', // Default Quasar secondary
          600: '#d81b60',
          700: '#c2185b',
          800: '#ad1457',
          900: '#880e4f',
        },
        accent: {
          50: '#f3e5f5',
          100: '#e1bee7',
          200: '#ce93d8',
          300: '#ba68c8',
          400: '#ab47bc',
          500: '#9c27b0', // Default Quasar accent
          600: '#8e24aa',
          700: '#7b1fa2',
          800: '#6a1b9a',
          900: '#4a148c',
        },
        positive: {
          50: '#e8f5e8',
          100: '#c8e6c9',
          200: '#a5d6a7',
          300: '#81c784',
          400: '#66bb6a',
          500: '#4caf50', // Default Quasar positive
          600: '#43a047',
          700: '#388e3c',
          800: '#2e7d32',
          900: '#1b5e20',
        },
        negative: {
          50: '#ffebee',
          100: '#ffcdd2',
          200: '#ef9a9a',
          300: '#e57373',
          400: '#ef5350',
          500: '#f44336', // Default Quasar negative
          600: '#e53935',
          700: '#d32f2f',
          800: '#c62828',
          900: '#b71c1c',
        },
        warning: {
          50: '#fff8e1',
          100: '#ffecb3',
          200: '#ffe082',
          300: '#ffd54f',
          400: '#ffca28',
          500: '#ffc107', // Default Quasar warning
          600: '#ffb300',
          700: '#ffa000',
          800: '#ff8f00',
          900: '#ff6f00',
        },
        info: {
          50: '#e1f5fe',
          100: '#b3e5fc',
          200: '#81d4fa',
          300: '#4fc3f7',
          400: '#29b6f6',
          500: '#03a9f4', // Default Quasar info
          600: '#039be5',
          700: '#0288d1',
          800: '#0277bd',
          900: '#01579b',
        },
        dark: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#eeeeee',
          300: '#e0e0e0',
          400: '#bdbdbd',
          500: '#9e9e9e',
          600: '#757575',
          700: '#616161',
          800: '#424242',
          900: '#212121', // Default Quasar dark
        },
      },
      spacing: {
        // Quasar spacing scale (based on 4px grid)
        'xs': '4px',
        'sm': '8px',
        'md': '16px',
        'lg': '24px',
        'xl': '48px',
      },
      borderRadius: {
        // Quasar border radius values
        'xs': '2px',
        'sm': '4px',
        'md': '8px',
        'lg': '16px',
        'xl': '24px',
      },
      fontFamily: {
        // Quasar default font families
        'sans': ['Roboto', 'Helvetica Neue', 'Helvetica', 'Arial', 'sans-serif'],
        'mono': ['Roboto Mono', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
      },
      fontSize: {
        // Quasar typography scale
        'caption': ['12px', { lineHeight: '16px' }],
        'body2': ['14px', { lineHeight: '20px' }],
        'body1': ['16px', { lineHeight: '24px' }],
        'subtitle2': ['14px', { lineHeight: '20px', fontWeight: '500' }],
        'subtitle1': ['16px', { lineHeight: '24px', fontWeight: '500' }],
        'h6': ['20px', { lineHeight: '28px', fontWeight: '500' }],
        'h5': ['24px', { lineHeight: '32px', fontWeight: '400' }],
        'h4': ['34px', { lineHeight: '40px', fontWeight: '400' }],
        'h3': ['48px', { lineHeight: '56px', fontWeight: '400' }],
        'h2': ['60px', { lineHeight: '68px', fontWeight: '300' }],
        'h1': ['96px', { lineHeight: '104px', fontWeight: '300' }],
      },
      boxShadow: {
        // Quasar elevation shadows
        '1': '0 1px 3px rgba(0,0,0,0.2), 0 1px 1px rgba(0,0,0,0.14), 0 2px 1px rgba(0,0,0,0.12)',
        '2': '0 1px 5px rgba(0,0,0,0.2), 0 2px 2px rgba(0,0,0,0.14), 0 3px 1px rgba(0,0,0,0.12)',
        '3': '0 1px 8px rgba(0,0,0,0.2), 0 3px 4px rgba(0,0,0,0.14), 0 3px 3px rgba(0,0,0,0.12)',
        '4': '0 2px 4px rgba(0,0,0,0.2), 0 4px 5px rgba(0,0,0,0.14), 0 1px 10px rgba(0,0,0,0.12)',
        '5': '0 3px 5px rgba(0,0,0,0.2), 0 5px 8px rgba(0,0,0,0.14), 0 1px 14px rgba(0,0,0,0.12)',
        '6': '0 3px 5px rgba(0,0,0,0.2), 0 6px 10px rgba(0,0,0,0.14), 0 1px 18px rgba(0,0,0,0.12)',
        '7': '0 4px 5px rgba(0,0,0,0.2), 0 7px 10px rgba(0,0,0,0.14), 0 2px 16px rgba(0,0,0,0.12)',
        '8': '0 5px 5px rgba(0,0,0,0.2), 0 8px 10px rgba(0,0,0,0.14), 0 3px 14px rgba(0,0,0,0.12)',
        '9': '0 5px 6px rgba(0,0,0,0.2), 0 9px 12px rgba(0,0,0,0.14), 0 3px 16px rgba(0,0,0,0.12)',
        '10': '0 6px 6px rgba(0,0,0,0.2), 0 10px 14px rgba(0,0,0,0.14), 0 4px 18px rgba(0,0,0,0.12)',
      },
    },
  },
  plugins: [
    // Add any additional Tailwind plugins here
    // For example: require('@tailwindcss/forms'), require('@tailwindcss/typography')
  ],
  // Ensure Tailwind doesn't conflict with Quasar's CSS
  corePlugins: {
    // Disable Tailwind's preflight to avoid conflicts with Quasar
    preflight: false,
  },
  // Add prefix to avoid conflicts with Quasar classes
  prefix: 'tw-',
  // Important selector to ensure Tailwind utilities have higher specificity
  important: true,
}
