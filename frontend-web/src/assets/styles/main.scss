/**
 * Smart Factory WMS - Main SCSS Styles
 * 
 * This file contains global SCSS styles and Quasar customizations
 * for the Smart Factory WMS application.
 */

// Quasar variables and mixins
@import 'quasar/src/css/variables.sass';

// Custom variables
$primary: #1976d2;
$secondary: #26a69a;
$accent: #9c27b0;
$dark: #1d1d1d;
$positive: #21ba45;
$negative: #c10015;
$info: #31ccec;
$warning: #f2c037;

// Authentication layout styles
.auth-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;

  .auth-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    padding: 2rem;
    width: 100%;
    max-width: 400px;

    @media (max-width: 640px) {
      padding: 1.5rem;
      margin: 1rem;
    }
  }

  .auth-logo {
    text-align: center;
    margin-bottom: 2rem;

    img {
      max-height: 60px;
      width: auto;
    }

    h1 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1a202c;
      margin-top: 0.5rem;
    }
  }

  .auth-form {
    .q-field {
      margin-bottom: 1rem;
    }

    .q-btn {
      width: 100%;
      height: 44px;
      font-weight: 500;
    }

    .forgot-password {
      text-align: center;
      margin-top: 1rem;

      .q-btn {
        width: auto;
        height: auto;
        padding: 0.5rem 0;
      }
    }

    .language-selector {
      margin-top: 1.5rem;
      text-align: center;

      .q-select {
        max-width: 200px;
        margin: 0 auto;
      }
    }
  }
}

// Loading states
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

// Form validation styles
.q-field--error {
  .q-field__control {
    border-color: #c10015 !important;
  }
}

.q-field--focused {
  .q-field__control {
    border-color: #1976d2 !important;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
  }
}

// Accessibility improvements
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// High contrast mode
@media (prefers-contrast: high) {
  .auth-layout {
    background: #000;
    
    .auth-card {
      border: 2px solid #fff;
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// Dark mode support (future enhancement)
@media (prefers-color-scheme: dark) {
  .auth-layout {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    
    .auth-card {
      background: #2d3748;
      color: #e2e8f0;
    }
  }
}
