/**
 * Smart Factory WMS - Main Application Styles
 * 
 * This file imports TailwindCSS and contains global styles
 * for the Smart Factory WMS application.
 */

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom TailwindCSS utilities with tw- prefix */
@layer utilities {
  .tw-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .tw-card {
    @apply bg-white rounded-lg shadow-md border border-gray-200;
  }
  
  .tw-btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
  }
  
  .tw-btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
  }
  
  .tw-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
  }
  
  .tw-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .tw-error {
    @apply text-red-600 text-sm mt-1;
  }
  
  .tw-success {
    @apply text-green-600 text-sm mt-1;
  }
  
  .tw-loading {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }
}

/* Global styles */
html {
  font-size: 16px;
  line-height: 1.6;
}

body {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background-color: #f8fafc;
  color: #1a202c;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tw-card {
    @apply border-2 border-gray-800;
  }
  
  .tw-btn-primary {
    @apply bg-blue-800 border-2 border-blue-900;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
