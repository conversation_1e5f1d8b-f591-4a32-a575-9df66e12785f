/**
 * Smart Factory WMS - Router Guards (Pinia Version)
 *
 * This file contains navigation guards for route protection,
 * authentication checks, and authorization validation using Pinia stores.
 */

import { useAuthStore } from '@/stores/auth'

/**
 * Authentication guard - requires user to be authenticated
 */
export const authGuard = async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Check if user is authenticated
  if (authStore.isAuthenticated) {
    // Verify token is still valid
    const isValid = await authStore.checkAuth()
    if (isValid) {
      next()
    } else {
      // Token invalid, redirect to login
      authStore.setRedirectRoute(to.fullPath)
      next('/login')
    }
  } else {
    // Not authenticated, store intended route and redirect to login
    authStore.setRedirectRoute(to.fullPath)
    next('/login')
  }
}

/**
 * Guest guard - requires user to be NOT authenticated
 */
export const guestGuard = async (to, from, next) => {
  const authStore = useAuthStore()
  
  if (authStore.isAuthenticated) {
    // Check if token is still valid
    const isValid = await authStore.checkAuth()
    if (isValid) {
      next('/dashboard')
    } else {
      // Token invalid, allow access to auth pages
      next()
    }
  } else {
    next()
  }
}

/**
 * Role guard - requires user to have specific roles
 */
export const roleGuard = (requiredRoles) => {
  return async (to, from, next) => {
    const authStore = useAuthStore()
    
    if (!authStore.isAuthenticated) {
      authStore.setRedirectRoute(to.fullPath)
      next('/login')
      return
    }

    // Verify authentication is still valid
    const isValid = await authStore.checkAuth()
    if (!isValid) {
      authStore.setRedirectRoute(to.fullPath)
      next('/login')
      return
    }

    const userRoles = authStore.userRoles
    const hasRequiredRole = requiredRoles.some(role => 
      userRoles.some(userRole => userRole.name === role)
    )

    if (hasRequiredRole) {
      next()
    } else {
      next('/unauthorized')
    }
  }
}

/**
 * Permission guard - requires user to have specific permissions
 */
export const permissionGuard = (requiredPermissions) => {
  return async (to, from, next) => {
    const authStore = useAuthStore()
    
    if (!authStore.isAuthenticated) {
      authStore.setRedirectRoute(to.fullPath)
      next('/login')
      return
    }

    // Verify authentication is still valid
    const isValid = await authStore.checkAuth()
    if (!isValid) {
      authStore.setRedirectRoute(to.fullPath)
      next('/login')
      return
    }

    const hasPermissions = authStore.hasPermissions(requiredPermissions)

    if (hasPermissions) {
      next()
    } else {
      next('/unauthorized')
    }
  }
}

/**
 * Admin guard - requires user to be an administrator
 */
export const adminGuard = async (to, from, next) => {
  const authStore = useAuthStore()
  
  if (!authStore.isAuthenticated) {
    authStore.setRedirectRoute(to.fullPath)
    next('/login')
    return
  }

  const isValid = await authStore.checkAuth()
  if (!isValid) {
    authStore.setRedirectRoute(to.fullPath)
    next('/login')
    return
  }

  const userRoles = authStore.userRoles
  const isAdmin = userRoles.some(role => role.name === 'admin')
  
  if (isAdmin) {
    next()
  } else {
    next('/unauthorized')
  }
}

/**
 * Manager guard - requires user to be a manager or admin
 */
export const managerGuard = async (to, from, next) => {
  const authStore = useAuthStore()
  
  if (!authStore.isAuthenticated) {
    authStore.setRedirectRoute(to.fullPath)
    next('/login')
    return
  }

  const isValid = await authStore.checkAuth()
  if (!isValid) {
    authStore.setRedirectRoute(to.fullPath)
    next('/login')
    return
  }

  const userRoles = authStore.userRoles
  const hasManagerAccess = userRoles.some(role => 
    role.name === 'admin' || role.name === 'manager'
  )
  
  if (hasManagerAccess) {
    next()
  } else {
    next('/unauthorized')
  }
}

/**
 * Composite guard - combines multiple guards with AND logic
 */
export const combineGuards = (...guards) => {
  return async (to, from, next) => {
    for (const guard of guards) {
      try {
        await new Promise((resolve, reject) => {
          guard(to, from, (result) => {
            if (result === false || (typeof result === 'string') || (typeof result === 'object')) {
              reject(result)
            } else {
              resolve()
            }
          })
        })
      } catch (result) {
        next(result)
        return
      }
    }
    next()
  }
}

/**
 * Async guard wrapper - handles async guard functions
 */
export const asyncGuard = (asyncGuardFn) => {
  return async (to, from, next) => {
    try {
      const result = await asyncGuardFn(to, from)
      if (result === true || result === undefined) {
        next()
      } else {
        next(result)
      }
    } catch (error) {
      console.error('Async guard error:', error)
      next('/server-error')
    }
  }
}
