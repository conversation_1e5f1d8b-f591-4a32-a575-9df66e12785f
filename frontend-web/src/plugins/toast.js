/**
 * Smart Factory WMS - Toast Notification Plugin
 * 
 * This plugin configures vue-toastification for showing
 * toast notifications throughout the application.
 */

import Toast, { POSITION } from 'vue-toastification'
import 'vue-toastification/dist/index.css'

// Toast configuration
const options = {
  position: POSITION.TOP_RIGHT,
  timeout: 5000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: 'button',
  icon: true,
  rtl: false,
  transition: 'Vue-Toastification__bounce',
  maxToasts: 5,
  newestOnTop: true,
  filterBeforeCreate: (toast, toasts) => {
    // Prevent duplicate toasts
    if (toasts.filter(t => t.content === toast.content).length !== 0) {
      return false
    }
    return toast
  }
}

export default {
  install(app) {
    app.use(Toast, options)
  }
}
