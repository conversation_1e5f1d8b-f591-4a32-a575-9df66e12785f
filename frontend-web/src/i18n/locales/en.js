/**
 * Smart Factory WMS - English Language File
 * 
 * This file contains all English translations for the application.
 */

export default {
  // Application
  app: {
    title: 'Smart Factory WMS',
    welcome: 'Welcome to Smart Factory WMS',
    loading: 'Loading...',
    error: 'An error occurred',
    success: 'Success',
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    view: 'View',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    import: 'Import',
    refresh: 'Refresh',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    submit: 'Submit',
    reset: 'Reset'
  },

  // Authentication
  auth: {
    title: 'Smart Factory WMS',
    welcome_message: 'Welcome to Smart Factory WMS',
    username_label: 'Username or Email',
    username_placeholder: 'Enter your username or email',
    password_label: 'Password',
    password_placeholder: 'Enter your password',
    remember_me: 'Remember me',
    sign_in_button: 'Sign In',
    signing_in: 'Signing in...',
    forgot_password: 'Forgot Password?',
    language_label: 'Language',
    
    // Validation messages
    validation: {
      username_required: 'Username or email is required',
      username_min_length: 'Username must be at least 3 characters',
      username_max_length: 'Username must not exceed 50 characters',
      username_invalid: 'Please enter a valid username or email',
      password_required: 'Password is required',
      password_min_length: 'Password must be at least 8 characters',
      password_max_length: 'Password must not exceed 128 characters'
    },

    // Error messages
    errors: {
      invalid_credentials: 'Invalid username or password',
      account_locked: 'Account is locked. Please contact administrator.',
      account_disabled: 'Account is disabled. Please contact administrator.',
      too_many_requests: 'Too many login attempts. Please try again later.',
      network_error: 'Network error. Please check your connection and try again.',
      server_error: 'Server error. Please try again later.',
      session_expired: 'Your session has expired. Please sign in again.',
      unauthorized: 'You are not authorized to access this resource.',
      forbidden: 'Access forbidden. You do not have permission to perform this action.'
    },

    // Success messages
    success: {
      login: 'Successfully signed in',
      logout: 'Successfully signed out',
      password_changed: 'Password changed successfully',
      password_reset_sent: 'Password reset instructions sent to your email',
      password_reset: 'Password reset successfully'
    },

    // Other auth pages
    register: 'Register',
    login: 'Login',
    logout: 'Logout',
    forgot_password_title: 'Forgot Password',
    reset_password_title: 'Reset Password'
  },

  // Navigation
  menu: {
    dashboard: 'Dashboard',
    inventory: 'Inventory',
    receiving: 'Receiving',
    shipment: 'Shipment',
    production: 'Production',
    reports: 'Reports',
    users: 'Users',
    settings: 'Settings',
    profile: 'Profile'
  },

  // Common form elements
  form: {
    required: 'This field is required',
    invalid_email: 'Please enter a valid email address',
    invalid_phone: 'Please enter a valid phone number',
    passwords_not_match: 'Passwords do not match',
    min_length: 'Minimum length is {min} characters',
    max_length: 'Maximum length is {max} characters',
    invalid_format: 'Invalid format',
    select_option: 'Please select an option',
    upload_file: 'Upload file',
    drag_drop_file: 'Drag and drop file here or click to browse'
  },

  // Error pages
  errors: {
    not_found: 'Page Not Found',
    not_found_message: 'The page you are looking for does not exist.',
    unauthorized: 'Unauthorized',
    unauthorized_message: 'You are not authorized to access this page.',
    server_error: 'Server Error',
    server_error_message: 'An internal server error occurred.',
    go_home: 'Go to Dashboard',
    try_again: 'Try Again'
  },

  // Languages
  languages: {
    en: 'English',
    ja: 'Japanese',
    zh: 'Chinese',
    vi: 'Vietnamese'
  },

  // Date and time
  datetime: {
    now: 'Now',
    today: 'Today',
    yesterday: 'Yesterday',
    tomorrow: 'Tomorrow',
    this_week: 'This week',
    last_week: 'Last week',
    this_month: 'This month',
    last_month: 'Last month',
    this_year: 'This year',
    last_year: 'Last year'
  },

  // Status
  status: {
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected',
    completed: 'Completed',
    cancelled: 'Cancelled',
    draft: 'Draft',
    published: 'Published'
  }
}
