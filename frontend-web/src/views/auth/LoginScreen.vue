<template>
  <div class="auth-layout">
    <q-card class="auth-card">
      <!-- Logo and Title -->
      <div class="auth-logo">
        <q-img
          src="/logo.png"
          alt="Smart Factory WMS Logo"
          class="tw-mx-auto tw-mb-4"
          style="max-height: 60px; width: auto;"
          no-spinner
          @error="logoError = true"
        >
          <template v-slot:error>
            <div class="tw-text-4xl tw-text-blue-600 tw-font-bold">
              SF
            </div>
          </template>
        </q-img>
        <h1 class="tw-text-2xl tw-font-semibold tw-text-gray-900 tw-text-center">
          {{ $t('auth.title') }}
        </h1>
        <p class="tw-text-gray-600 tw-text-center tw-mt-2">
          {{ $t('auth.welcome_message') }}
        </p>
      </div>

      <!-- Login Form -->
      <q-form
        @submit="handleLogin"
        @reset="resetForm"
        class="auth-form"
        greedy
      >
        <!-- Username/Email Field -->
        <q-input
          v-model="loginForm.username"
          :label="$t('auth.username_label')"
          :placeholder="$t('auth.username_placeholder')"
          :rules="validationRules.username"
          :error="!!errors.username"
          :error-message="errors.username"
          outlined
          dense
          autocomplete="username"
          :aria-label="$t('auth.username_label')"
          @blur="validateField('username', loginForm.username)"
          @input="errors.username && validateField('username', loginForm.username)"
        >
          <template v-slot:prepend>
            <q-icon name="person" />
          </template>
        </q-input>

        <!-- Password Field -->
        <q-input
          v-model="loginForm.password"
          :label="$t('auth.password_label')"
          :placeholder="$t('auth.password_placeholder')"
          :type="showPassword ? 'text' : 'password'"
          :rules="validationRules.password"
          :error="!!errors.password"
          :error-message="errors.password"
          outlined
          dense
          autocomplete="current-password"
          :aria-label="$t('auth.password_label')"
          @blur="validateField('password', loginForm.password)"
          @input="errors.password && validateField('password', loginForm.password)"
        >
          <template v-slot:prepend>
            <q-icon name="lock" />
          </template>
          <template v-slot:append>
            <q-btn
              :icon="showPassword ? 'visibility_off' : 'visibility'"
              flat
              round
              dense
              @click="togglePasswordVisibility"
              :aria-label="showPassword ? 'Hide password' : 'Show password'"
              tabindex="-1"
            />
          </template>
        </q-input>

        <!-- Remember Me Checkbox -->
        <q-checkbox
          v-model="loginForm.remember_me"
          :label="$t('auth.remember_me')"
          class="tw-mb-4"
          :aria-label="$t('auth.remember_me')"
        />

        <!-- Login Button -->
        <q-btn
          type="submit"
          :label="isSubmitting ? $t('auth.signing_in') : $t('auth.sign_in_button')"
          color="primary"
          class="full-width tw-mb-4"
          size="md"
          :loading="isSubmitting || authStore.isLoading"
          :disable="!canSubmit || loginAttemptsExceeded"
          :aria-label="$t('auth.sign_in_button')"
        >
          <template v-slot:loading>
            <q-spinner-hourglass class="on-left" />
            {{ $t('auth.signing_in') }}
          </template>
        </q-btn>

        <!-- Rate Limiting Warning -->
        <q-banner
          v-if="loginAttemptsExceeded && timeUntilNextAttempt > 0"
          class="tw-mb-4 text-negative"
          dense
          rounded
        >
          <template v-slot:avatar>
            <q-icon name="warning" color="negative" />
          </template>
          {{ $t('auth.errors.too_many_requests') }}
          {{ Math.ceil(timeUntilNextAttempt / (60 * 1000)) }} {{ $t('datetime.minutes') }}.
        </q-banner>

        <!-- Forgot Password Link -->
        <div class="forgot-password">
          <q-btn
            :label="$t('auth.forgot_password')"
            flat
            color="primary"
            size="sm"
            @click="$router.push('/forgot-password')"
            :aria-label="$t('auth.forgot_password')"
          />
        </div>

        <!-- Language Selector -->
        <div class="language-selector">
          <q-select
            v-model="selectedLanguage"
            :options="languageOptions"
            :label="$t('auth.language_label')"
            outlined
            dense
            emit-value
            map-options
            @update:model-value="changeLanguage"
            :aria-label="$t('auth.language_label')"
          >
            <template v-slot:selected-item="scope">
              <span>{{ scope.opt.flag }} {{ scope.opt.name }}</span>
            </template>
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section>
                  <span>{{ scope.opt.flag }} {{ scope.opt.name }}</span>
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </div>
      </q-form>
    </q-card>

    <!-- Loading Overlay -->
    <q-inner-loading
      :showing="authStore.isLoading"
      label="Please wait..."
      label-class="text-teal"
      label-style="font-size: 1.1em"
    />
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'
import { useLanguage } from '@/composables/useLanguage'

export default {
  name: 'LoginScreen',
  setup() {
    const router = useRouter()
    const logoError = ref(false)
    
    // Composables
    const {
      loginForm,
      showPassword,
      isSubmitting,
      errors,
      isFormValid,
      canSubmit,
      loginAttemptsExceeded,
      timeUntilNextAttempt,
      validationRules,
      validateField,
      handleLogin,
      togglePasswordVisibility,
      resetForm,
      authStore
    } = useAuth()

    const {
      currentLanguage,
      languageOptions,
      changeLanguage: changeLang
    } = useLanguage()

    // Language handling
    const selectedLanguage = ref(currentLanguage.value)
    
    const changeLanguage = (newLanguage) => {
      if (changeLang(newLanguage)) {
        selectedLanguage.value = newLanguage
      }
    }

    // Auto-refresh timer for rate limiting
    let refreshTimer = null
    
    const startRefreshTimer = () => {
      if (timeUntilNextAttempt.value > 0) {
        refreshTimer = setInterval(() => {
          if (timeUntilNextAttempt.value <= 0) {
            clearInterval(refreshTimer)
            refreshTimer = null
          }
        }, 1000)
      }
    }

    // Lifecycle
    onMounted(() => {
      // Check if user is already authenticated
      if (authStore.isAuthenticated) {
        router.push('/dashboard')
        return
      }

      // Initialize auth store
      authStore.initializeAuth()
      
      // Set up refresh timer if needed
      startRefreshTimer()
      
      // Focus on username field
      setTimeout(() => {
        const usernameField = document.querySelector('input[autocomplete="username"]')
        if (usernameField) {
          usernameField.focus()
        }
      }, 100)
    })

    onUnmounted(() => {
      if (refreshTimer) {
        clearInterval(refreshTimer)
      }
    })

    return {
      // Data
      logoError,
      selectedLanguage,
      
      // Auth composable
      loginForm,
      showPassword,
      isSubmitting,
      errors,
      isFormValid,
      canSubmit,
      loginAttemptsExceeded,
      timeUntilNextAttempt,
      validationRules,
      validateField,
      handleLogin,
      togglePasswordVisibility,
      resetForm,
      authStore,
      
      // Language composable
      languageOptions,
      changeLanguage
    }
  }
}
</script>

<style lang="scss" scoped>
.auth-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;

  .auth-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    padding: 2rem;
    width: 100%;
    max-width: 400px;

    @media (max-width: 640px) {
      padding: 1.5rem;
      margin: 1rem;
    }
  }

  .auth-logo {
    text-align: center;
    margin-bottom: 2rem;

    h1 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1a202c;
      margin-top: 0.5rem;
    }
  }

  .auth-form {
    .q-field {
      margin-bottom: 1rem;
    }

    .q-btn {
      height: 44px;
      font-weight: 500;
    }

    .forgot-password {
      text-align: center;
      margin-top: 1rem;

      .q-btn {
        width: auto;
        height: auto;
        padding: 0.5rem 0;
      }
    }

    .language-selector {
      margin-top: 1.5rem;
      text-align: center;

      .q-select {
        max-width: 200px;
        margin: 0 auto;
      }
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .auth-layout {
    background: #000;
    
    .auth-card {
      border: 2px solid #fff;
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
