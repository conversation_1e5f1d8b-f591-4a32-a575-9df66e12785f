/**
 * Smart Factory WMS - Authentication Store Module
 * 
 * This module handles user authentication, authorization,
 * and user session management.
 */

import api from '@/services/api'
import { jwtDecode } from 'jwt-decode'

const state = {
  token: null,
  refreshToken: null,
  user: null,
  isLoading: false,
  redirectRoute: null,
  loginAttempts: 0,
  lastLoginAttempt: null
}

const getters = {
  isAuthenticated: (state) => !!state.token && !!state.user,
  
  userId: (state) => state.user?.id,
  
  userName: (state) => state.user?.name,
  
  userEmail: (state) => state.user?.email,
  
  userRoles: (state) => state.user?.roles || [],
  
  userPermissions: (state) => {
    if (!state.user?.roles) return []
    return state.user.roles.flatMap(role => role.permissions || [])
  },
  
  hasRole: (state, getters) => (role) => {
    return getters.userRoles.some(userRole => userRole.name === role)
  },
  
  hasPermission: (state, getters) => (permission) => {
    return getters.userPermissions.some(userPerm => userPerm.name === permission)
  },
  
  hasPermissions: (state, getters) => (permissions) => {
    if (!Array.isArray(permissions)) return false
    return permissions.every(permission => getters.hasPermission(permission))
  },
  
  hasAnyPermission: (state, getters) => (permissions) => {
    if (!Array.isArray(permissions)) return false
    return permissions.some(permission => getters.hasPermission(permission))
  },
  
  isTokenExpired: (state) => {
    if (!state.token) return true
    try {
      const decoded = jwtDecode(state.token)
      return decoded.exp * 1000 < Date.now()
    } catch {
      return true
    }
  },
  
  tokenExpiresIn: (state) => {
    if (!state.token) return 0
    try {
      const decoded = jwtDecode(state.token)
      return Math.max(0, decoded.exp * 1000 - Date.now())
    } catch {
      return 0
    }
  }
}

const mutations = {
  SET_LOADING(state, loading) {
    state.isLoading = loading
  },
  
  SET_TOKEN(state, token) {
    state.token = token
    if (token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`
    } else {
      delete api.defaults.headers.common['Authorization']
    }
  },
  
  SET_REFRESH_TOKEN(state, refreshToken) {
    state.refreshToken = refreshToken
  },
  
  SET_USER(state, user) {
    state.user = user
  },
  
  SET_REDIRECT_ROUTE(state, route) {
    state.redirectRoute = route
  },
  
  CLEAR_REDIRECT_ROUTE(state) {
    state.redirectRoute = null
  },
  
  INCREMENT_LOGIN_ATTEMPTS(state) {
    state.loginAttempts++
    state.lastLoginAttempt = Date.now()
  },
  
  RESET_LOGIN_ATTEMPTS(state) {
    state.loginAttempts = 0
    state.lastLoginAttempt = null
  },
  
  CLEAR_AUTH(state) {
    state.token = null
    state.refreshToken = null
    state.user = null
    state.redirectRoute = null
    delete api.defaults.headers.common['Authorization']
  }
}

const actions = {
  async login({ commit, dispatch }, { email, password, rememberMe = false }) {
    try {
      commit('SET_LOADING', true)
      
      const response = await api.post('/auth/login', {
        email,
        password,
        remember_me: rememberMe
      })
      
      const { access_token, refresh_token, user } = response.data
      
      commit('SET_TOKEN', access_token)
      commit('SET_REFRESH_TOKEN', refresh_token)
      commit('SET_USER', user)
      commit('RESET_LOGIN_ATTEMPTS')
      
      // Set up token refresh timer
      dispatch('setupTokenRefresh')
      
      return { success: true, user }
    } catch (error) {
      commit('INCREMENT_LOGIN_ATTEMPTS')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  async register({ commit }, userData) {
    try {
      commit('SET_LOADING', true)
      
      const response = await api.post('/auth/register', userData)
      
      return { success: true, data: response.data }
    } catch (error) {
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  async logout({ commit, dispatch }) {
    try {
      // Call logout endpoint to invalidate token on server
      if (state.token) {
        await api.post('/auth/logout')
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      commit('CLEAR_AUTH')
      dispatch('clearTokenRefresh')
      
      // Clear localStorage
      localStorage.removeItem('auth')
    }
  },
  
  async refreshToken({ commit, dispatch, state }) {
    if (!state.refreshToken) {
      throw new Error('No refresh token available')
    }
    
    try {
      const response = await api.post('/auth/refresh', {
        refresh_token: state.refreshToken
      })
      
      const { access_token, refresh_token } = response.data
      
      commit('SET_TOKEN', access_token)
      if (refresh_token) {
        commit('SET_REFRESH_TOKEN', refresh_token)
      }
      
      // Set up new token refresh timer
      dispatch('setupTokenRefresh')
      
      return access_token
    } catch (error) {
      // Refresh failed, logout user
      dispatch('logout')
      throw error
    }
  },
  
  async fetchUser({ commit, state }) {
    if (!state.token) return null
    
    try {
      const response = await api.get('/auth/me')
      const user = response.data
      
      commit('SET_USER', user)
      return user
    } catch (error) {
      console.error('Failed to fetch user:', error)
      throw error
    }
  },
  
  async updateProfile({ commit, state }, profileData) {
    try {
      commit('SET_LOADING', true)
      
      const response = await api.put('/auth/profile', profileData)
      const updatedUser = response.data
      
      commit('SET_USER', updatedUser)
      return updatedUser
    } catch (error) {
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  async changePassword({ commit }, { currentPassword, newPassword }) {
    try {
      commit('SET_LOADING', true)
      
      await api.post('/auth/change-password', {
        current_password: currentPassword,
        new_password: newPassword
      })
      
      return { success: true }
    } catch (error) {
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  async forgotPassword({ commit }, email) {
    try {
      commit('SET_LOADING', true)
      
      await api.post('/auth/forgot-password', { email })
      
      return { success: true }
    } catch (error) {
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  async resetPassword({ commit }, { token, password }) {
    try {
      commit('SET_LOADING', true)
      
      await api.post('/auth/reset-password', {
        token,
        password
      })
      
      return { success: true }
    } catch (error) {
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  setupTokenRefresh({ dispatch, getters }) {
    // Clear any existing timer
    dispatch('clearTokenRefresh')
    
    if (!getters.isAuthenticated || getters.isTokenExpired) return
    
    const expiresIn = getters.tokenExpiresIn
    const refreshTime = Math.max(0, expiresIn - 5 * 60 * 1000) // Refresh 5 minutes before expiry
    
    if (refreshTime > 0) {
      state.refreshTimer = setTimeout(() => {
        dispatch('refreshToken').catch(() => {
          // Refresh failed, will be handled in refreshToken action
        })
      }, refreshTime)
    }
  },
  
  clearTokenRefresh() {
    if (state.refreshTimer) {
      clearTimeout(state.refreshTimer)
      state.refreshTimer = null
    }
  },
  
  async checkAuth({ dispatch, getters, state }) {
    if (!state.token) return false
    
    if (getters.isTokenExpired) {
      try {
        await dispatch('refreshToken')
        return true
      } catch {
        return false
      }
    }
    
    // Fetch user data if not available
    if (!state.user) {
      try {
        await dispatch('fetchUser')
      } catch {
        return false
      }
    }
    
    dispatch('setupTokenRefresh')
    return true
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
