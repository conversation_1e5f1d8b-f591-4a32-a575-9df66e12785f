/**
 * Smart Factory WMS - Authentication Composable
 * 
 * This composable provides authentication logic, form validation,
 * and API integration for login functionality.
 */

import { ref, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useQuasar } from 'quasar'
import { useAuthStore } from '@/stores/auth'

export function useAuth() {
  const router = useRouter()
  const { t } = useI18n()
  const $q = useQuasar()
  const authStore = useAuthStore()

  // Form state
  const loginForm = reactive({
    username: '',
    password: '',
    remember_me: false
  })

  const forgotPasswordForm = reactive({
    email: ''
  })

  const resetPasswordForm = reactive({
    token: '',
    new_password: '',
    confirm_password: ''
  })

  // UI state
  const showPassword = ref(false)
  const isSubmitting = ref(false)
  const errors = ref({})

  // Validation rules
  const validationRules = {
    username: [
      val => !!val || t('auth.validation.username_required'),
      val => (val && val.length >= 3) || t('auth.validation.username_min_length'),
      val => (val && val.length <= 50) || t('auth.validation.username_max_length'),
      val => {
        // Check if it's a valid email or username
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        const usernameRegex = /^[a-zA-Z0-9._]+$/
        return (emailRegex.test(val) || usernameRegex.test(val)) || t('auth.validation.username_invalid')
      }
    ],
    password: [
      val => !!val || t('auth.validation.password_required'),
      val => (val && val.length >= 8) || t('auth.validation.password_min_length'),
      val => (val && val.length <= 128) || t('auth.validation.password_max_length')
    ],
    email: [
      val => !!val || t('form.required'),
      val => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return emailRegex.test(val) || t('form.invalid_email')
      }
    ],
    confirmPassword: [
      val => !!val || t('form.required'),
      val => val === resetPasswordForm.new_password || t('form.passwords_not_match')
    ]
  }

  // Computed properties
  const isFormValid = computed(() => {
    return loginForm.username.length >= 3 && 
           loginForm.password.length >= 8 &&
           !Object.keys(errors.value).length
  })

  const canSubmit = computed(() => {
    return isFormValid.value && !isSubmitting.value && !authStore.isLoading
  })

  const loginAttemptsExceeded = computed(() => {
    return authStore.loginAttempts >= 5
  })

  const timeUntilNextAttempt = computed(() => {
    if (!authStore.lastLoginAttempt || authStore.loginAttempts < 5) return 0
    const timeSinceLastAttempt = Date.now() - authStore.lastLoginAttempt
    const lockoutDuration = 15 * 60 * 1000 // 15 minutes
    return Math.max(0, lockoutDuration - timeSinceLastAttempt)
  })

  // Methods
  const validateField = (field, value) => {
    const rules = validationRules[field]
    if (!rules) return true

    for (const rule of rules) {
      const result = rule(value)
      if (result !== true) {
        errors.value[field] = result
        return false
      }
    }

    delete errors.value[field]
    return true
  }

  const validateForm = () => {
    errors.value = {}
    let isValid = true

    // Validate username
    if (!validateField('username', loginForm.username)) {
      isValid = false
    }

    // Validate password
    if (!validateField('password', loginForm.password)) {
      isValid = false
    }

    return isValid
  }

  const handleLogin = async () => {
    if (!validateForm()) {
      return
    }

    if (loginAttemptsExceeded.value && timeUntilNextAttempt.value > 0) {
      const minutes = Math.ceil(timeUntilNextAttempt.value / (60 * 1000))
      $q.notify({
        type: 'negative',
        message: t('auth.errors.too_many_requests') + ` ${minutes} ${t('datetime.minutes')}.`,
        position: 'top'
      })
      return
    }

    try {
      isSubmitting.value = true
      
      const result = await authStore.login({
        username: loginForm.username,
        password: loginForm.password,
        remember_me: loginForm.remember_me
      })

      if (result.success) {
        $q.notify({
          type: 'positive',
          message: t('auth.success.login'),
          position: 'top'
        })

        // Redirect to intended route or dashboard
        const redirectTo = authStore.redirectRoute || '/dashboard'
        authStore.clearRedirectRoute()
        await router.push(redirectTo)
      }
    } catch (error) {
      handleAuthError(error)
    } finally {
      isSubmitting.value = false
    }
  }

  const handleForgotPassword = async () => {
    if (!validateField('email', forgotPasswordForm.email)) {
      return
    }

    try {
      isSubmitting.value = true
      
      await authStore.forgotPassword(forgotPasswordForm.email)
      
      $q.notify({
        type: 'positive',
        message: t('auth.success.password_reset_sent'),
        position: 'top'
      })

      // Reset form
      forgotPasswordForm.email = ''
    } catch (error) {
      handleAuthError(error)
    } finally {
      isSubmitting.value = false
    }
  }

  const handleResetPassword = async () => {
    if (!validateField('password', resetPasswordForm.new_password) ||
        !validateField('confirmPassword', resetPasswordForm.confirm_password)) {
      return
    }

    try {
      isSubmitting.value = true
      
      await authStore.resetPassword({
        token: resetPasswordForm.token,
        new_password: resetPasswordForm.new_password
      })
      
      $q.notify({
        type: 'positive',
        message: t('auth.success.password_reset'),
        position: 'top'
      })

      // Redirect to login
      await router.push('/login')
    } catch (error) {
      handleAuthError(error)
    } finally {
      isSubmitting.value = false
    }
  }

  const handleAuthError = (error) => {
    let message = t('auth.errors.server_error')

    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      switch (status) {
        case 401:
          message = t('auth.errors.invalid_credentials')
          break
        case 404:
          message = t('auth.errors.invalid_credentials')
          break
        case 423:
          message = t('auth.errors.account_locked')
          break
        case 429:
          message = t('auth.errors.too_many_requests')
          break
        case 422:
          // Validation errors
          if (data.detail && Array.isArray(data.detail)) {
            data.detail.forEach(error => {
              const field = error.loc[error.loc.length - 1]
              errors.value[field] = error.msg
            })
            return
          }
          break
        default:
          if (data.message) {
            message = data.message
          }
      }
    } else if (error.code === 'NETWORK_ERROR') {
      message = t('auth.errors.network_error')
    }

    $q.notify({
      type: 'negative',
      message,
      position: 'top',
      timeout: 5000
    })
  }

  const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value
  }

  const resetForm = () => {
    loginForm.username = ''
    loginForm.password = ''
    loginForm.remember_me = false
    errors.value = {}
  }

  const logout = async () => {
    try {
      await authStore.logout()
      
      $q.notify({
        type: 'positive',
        message: t('auth.success.logout'),
        position: 'top'
      })

      await router.push('/login')
    } catch (error) {
      console.error('Logout error:', error)
      // Force logout even if API call fails
      authStore.clearAuth()
      await router.push('/login')
    }
  }

  return {
    // Form state
    loginForm,
    forgotPasswordForm,
    resetPasswordForm,
    
    // UI state
    showPassword,
    isSubmitting,
    errors,
    
    // Computed
    isFormValid,
    canSubmit,
    loginAttemptsExceeded,
    timeUntilNextAttempt,
    
    // Validation
    validationRules,
    validateField,
    validateForm,
    
    // Actions
    handleLogin,
    handleForgotPassword,
    handleResetPassword,
    handleAuthError,
    togglePasswordVisibility,
    resetForm,
    logout,
    
    // Store access
    authStore
  }
}
