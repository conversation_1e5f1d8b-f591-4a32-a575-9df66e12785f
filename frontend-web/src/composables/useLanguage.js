/**
 * Smart Factory WMS - Language Composable
 * 
 * This composable provides language switching functionality
 * and language-related utilities.
 */

import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { supportedLanguages, setLanguage } from '@/plugins/i18n'

export function useLanguage() {
  const { locale, t } = useI18n()
  
  // Current language
  const currentLanguage = computed(() => locale.value)
  
  // Available languages
  const availableLanguages = computed(() => supportedLanguages)
  
  // Current language info
  const currentLanguageInfo = computed(() => {
    return supportedLanguages.find(lang => lang.code === currentLanguage.value) || supportedLanguages[0]
  })
  
  // Language options for select components
  const languageOptions = computed(() => {
    return supportedLanguages.map(lang => ({
      label: `${lang.flag} ${lang.name}`,
      value: lang.code,
      flag: lang.flag,
      name: lang.name
    }))
  })

  // Methods
  const changeLanguage = (languageCode) => {
    if (setLanguage(languageCode)) {
      // Language changed successfully
      return true
    }
    return false
  }

  const getLanguageName = (code) => {
    const lang = supportedLanguages.find(l => l.code === code)
    return lang ? lang.name : code
  }

  const getLanguageFlag = (code) => {
    const lang = supportedLanguages.find(l => l.code === code)
    return lang ? lang.flag : '🌐'
  }

  const isRTL = computed(() => {
    // Add RTL language codes here if needed in the future
    const rtlLanguages = ['ar', 'he', 'fa']
    return rtlLanguages.includes(currentLanguage.value)
  })

  const formatDate = (date, format = 'short') => {
    if (!date) return ''
    
    const dateObj = date instanceof Date ? date : new Date(date)
    
    return new Intl.DateTimeFormat(currentLanguage.value, {
      ...(format === 'short' ? {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      } : {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }).format(dateObj)
  }

  const formatNumber = (number, options = {}) => {
    if (number === null || number === undefined) return ''
    
    return new Intl.NumberFormat(currentLanguage.value, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
      ...options
    }).format(number)
  }

  const formatCurrency = (amount, currency = null) => {
    if (amount === null || amount === undefined) return ''
    
    // Default currencies by language
    const defaultCurrencies = {
      en: 'USD',
      ja: 'JPY',
      zh: 'CNY',
      vi: 'VND'
    }
    
    const currencyCode = currency || defaultCurrencies[currentLanguage.value] || 'USD'
    
    return new Intl.NumberFormat(currentLanguage.value, {
      style: 'currency',
      currency: currencyCode
    }).format(amount)
  }

  const formatRelativeTime = (date) => {
    if (!date) return ''
    
    const dateObj = date instanceof Date ? date : new Date(date)
    const now = new Date()
    const diffInSeconds = Math.floor((now - dateObj) / 1000)
    
    const rtf = new Intl.RelativeTimeFormat(currentLanguage.value, { numeric: 'auto' })
    
    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second')
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')
    } else if (diffInSeconds < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')
    } else if (diffInSeconds < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month')
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year')
    }
  }

  // Initialize language from user preferences or localStorage
  const initializeLanguage = () => {
    const savedLanguage = localStorage.getItem('language')
    const browserLanguage = navigator.language.split('-')[0]
    const userLanguage = savedLanguage || browserLanguage || 'en'
    
    if (supportedLanguages.some(lang => lang.code === userLanguage)) {
      changeLanguage(userLanguage)
    } else {
      changeLanguage('en')
    }
  }

  return {
    // State
    currentLanguage,
    availableLanguages,
    currentLanguageInfo,
    languageOptions,
    isRTL,
    
    // Methods
    changeLanguage,
    getLanguageName,
    getLanguageFlag,
    formatDate,
    formatNumber,
    formatCurrency,
    formatRelativeTime,
    initializeLanguage,
    
    // i18n access
    t
  }
}
