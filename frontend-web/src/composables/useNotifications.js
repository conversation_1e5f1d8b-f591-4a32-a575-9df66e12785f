/**
 * Smart Factory WMS - Notifications Composable
 * 
 * This composable provides a unified interface for showing notifications
 * and dialogs using Quasar's built-in Notify and Dialog plugins.
 * It replaces the previous vue-toastification implementation.
 */

import { useQuasar } from 'quasar'
import { useI18n } from 'vue-i18n'

export function useNotifications() {
  const $q = useQuasar()
  const { t } = useI18n()

  /**
   * Show a success notification
   * @param {string} message - The message to display
   * @param {Object} options - Additional options for the notification
   */
  const success = (message, options = {}) => {
    $q.notify({
      type: 'positive',
      message,
      position: 'top-right',
      timeout: 5000,
      actions: [
        {
          icon: 'close',
          color: 'white',
          round: true,
          handler: () => {}
        }
      ],
      ...options
    })
  }

  /**
   * Show an error notification
   * @param {string} message - The message to display
   * @param {Object} options - Additional options for the notification
   */
  const error = (message, options = {}) => {
    $q.notify({
      type: 'negative',
      message,
      position: 'top-right',
      timeout: 8000,
      actions: [
        {
          icon: 'close',
          color: 'white',
          round: true,
          handler: () => {}
        }
      ],
      ...options
    })
  }

  /**
   * Show a warning notification
   * @param {string} message - The message to display
   * @param {Object} options - Additional options for the notification
   */
  const warning = (message, options = {}) => {
    $q.notify({
      type: 'warning',
      message,
      position: 'top-right',
      timeout: 6000,
      actions: [
        {
          icon: 'close',
          color: 'white',
          round: true,
          handler: () => {}
        }
      ],
      ...options
    })
  }

  /**
   * Show an info notification
   * @param {string} message - The message to display
   * @param {Object} options - Additional options for the notification
   */
  const info = (message, options = {}) => {
    $q.notify({
      type: 'info',
      message,
      position: 'top-right',
      timeout: 5000,
      actions: [
        {
          icon: 'close',
          color: 'white',
          round: true,
          handler: () => {}
        }
      ],
      ...options
    })
  }

  /**
   * Show a confirmation dialog
   * @param {string} title - The dialog title
   * @param {string} message - The dialog message
   * @param {Object} options - Additional options for the dialog
   * @returns {Promise} Promise that resolves with the user's choice
   */
  const confirm = (title, message, options = {}) => {
    return $q.dialog({
      title,
      message,
      cancel: true,
      persistent: true,
      ok: {
        label: t('app.confirm'),
        color: 'primary'
      },
      cancel: {
        label: t('app.cancel'),
        color: 'grey',
        flat: true
      },
      ...options
    })
  }

  /**
   * Show an alert dialog
   * @param {string} title - The dialog title
   * @param {string} message - The dialog message
   * @param {Object} options - Additional options for the dialog
   * @returns {Promise} Promise that resolves when the dialog is closed
   */
  const alert = (title, message, options = {}) => {
    return $q.dialog({
      title,
      message,
      ok: {
        label: t('app.close'),
        color: 'primary'
      },
      ...options
    })
  }

  /**
   * Show a prompt dialog
   * @param {string} title - The dialog title
   * @param {string} message - The dialog message
   * @param {Object} options - Additional options for the dialog
   * @returns {Promise} Promise that resolves with the user's input
   */
  const prompt = (title, message, options = {}) => {
    return $q.dialog({
      title,
      message,
      prompt: {
        model: '',
        type: 'text'
      },
      cancel: true,
      persistent: true,
      ok: {
        label: t('app.submit'),
        color: 'primary'
      },
      cancel: {
        label: t('app.cancel'),
        color: 'grey',
        flat: true
      },
      ...options
    })
  }

  /**
   * Show a loading notification
   * @param {string} message - The loading message
   * @param {Object} options - Additional options for the notification
   */
  const loading = (message, options = {}) => {
    return $q.notify({
      type: 'ongoing',
      message,
      position: 'top-right',
      spinner: true,
      timeout: 0, // Don't auto-dismiss
      actions: [
        {
          icon: 'close',
          color: 'white',
          round: true,
          handler: () => {}
        }
      ],
      ...options
    })
  }

  return {
    success,
    error,
    warning,
    info,
    confirm,
    alert,
    prompt,
    loading
  }
}
