/**
 * Smart Factory WMS - Authentication Store (Pinia)
 * 
 * This store handles user authentication, authorization,
 * and user session management using Pinia.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'
import { jwtDecode } from 'jwt-decode'

export const useAuthStore = defineStore('auth', () => {
  // State
  const token = ref(null)
  const refreshToken = ref(null)
  const user = ref(null)
  const isLoading = ref(false)
  const redirectRoute = ref(null)
  const loginAttempts = ref(0)
  const lastLoginAttempt = ref(null)
  const refreshTimer = ref(null)

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  
  const userId = computed(() => user.value?.id)
  
  const userName = computed(() => user.value?.username)
  
  const userEmail = computed(() => user.value?.email)
  
  const userRoles = computed(() => user.value?.roles || [])
  
  const userPermissions = computed(() => {
    if (!user.value?.roles) return []
    return user.value.roles.flatMap(role => role.permissions || [])
  })
  
  const hasRole = computed(() => (role) => {
    return userRoles.value.some(userRole => userRole.name === role)
  })
  
  const hasPermission = computed(() => (permission) => {
    return userPermissions.value.some(userPerm => userPerm.name === permission)
  })
  
  const hasPermissions = computed(() => (permissions) => {
    if (!Array.isArray(permissions)) return false
    return permissions.every(permission => hasPermission.value(permission))
  })
  
  const hasAnyPermission = computed(() => (permissions) => {
    if (!Array.isArray(permissions)) return false
    return permissions.some(permission => hasPermission.value(permission))
  })
  
  const isTokenExpired = computed(() => {
    if (!token.value) return true
    try {
      const decoded = jwtDecode(token.value)
      return decoded.exp * 1000 < Date.now()
    } catch {
      return true
    }
  })
  
  const tokenExpiresIn = computed(() => {
    if (!token.value) return 0
    try {
      const decoded = jwtDecode(token.value)
      return Math.max(0, decoded.exp * 1000 - Date.now())
    } catch {
      return 0
    }
  })

  // Actions
  const setToken = (newToken) => {
    token.value = newToken
    if (newToken) {
      api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      // Store in localStorage for persistence
      localStorage.setItem('auth_token', newToken)
    } else {
      delete api.defaults.headers.common['Authorization']
      localStorage.removeItem('auth_token')
    }
  }

  const setRefreshToken = (newRefreshToken) => {
    refreshToken.value = newRefreshToken
    if (newRefreshToken) {
      localStorage.setItem('refresh_token', newRefreshToken)
    } else {
      localStorage.removeItem('refresh_token')
    }
  }

  const setUser = (newUser) => {
    user.value = newUser
    if (newUser) {
      localStorage.setItem('user', JSON.stringify(newUser))
    } else {
      localStorage.removeItem('user')
    }
  }

  const setRedirectRoute = (route) => {
    redirectRoute.value = route
  }

  const clearRedirectRoute = () => {
    redirectRoute.value = null
  }

  const incrementLoginAttempts = () => {
    loginAttempts.value++
    lastLoginAttempt.value = Date.now()
  }

  const resetLoginAttempts = () => {
    loginAttempts.value = 0
    lastLoginAttempt.value = null
  }

  const clearAuth = () => {
    token.value = null
    refreshToken.value = null
    user.value = null
    redirectRoute.value = null
    delete api.defaults.headers.common['Authorization']
    
    // Clear localStorage
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user')
  }

  const login = async ({ username, password, remember_me = false }) => {
    try {
      isLoading.value = true
      
      const response = await api.post('/api/v1/auth/login', {
        username,
        password,
        remember_me
      }, {
        headers: {
          'X-Request-ID': crypto.randomUUID()
        }
      })
      
      const { access_token, refresh_token, user: userData } = response.data
      
      setToken(access_token)
      setRefreshToken(refresh_token)
      setUser(userData)
      resetLoginAttempts()
      
      // Set up token refresh timer
      setupTokenRefresh()
      
      return { success: true, user: userData }
    } catch (error) {
      incrementLoginAttempts()
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      // Call logout endpoint to invalidate token on server
      if (token.value) {
        await api.post('/api/v1/auth/logout')
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      clearAuth()
      clearTokenRefresh()
    }
  }

  const refreshTokenAction = async () => {
    if (!refreshToken.value) {
      throw new Error('No refresh token available')
    }
    
    try {
      const response = await api.post('/api/v1/auth/refresh', {
        refresh_token: refreshToken.value
      })
      
      const { access_token, refresh_token: newRefreshToken } = response.data
      
      setToken(access_token)
      if (newRefreshToken) {
        setRefreshToken(newRefreshToken)
      }
      
      // Set up new token refresh timer
      setupTokenRefresh()
      
      return access_token
    } catch (error) {
      // Refresh failed, logout user
      await logout()
      throw error
    }
  }

  const fetchUser = async () => {
    if (!token.value) return null
    
    try {
      const response = await api.get('/api/v1/auth/me')
      const userData = response.data
      
      setUser(userData)
      return userData
    } catch (error) {
      console.error('Failed to fetch user:', error)
      throw error
    }
  }

  const forgotPassword = async (email) => {
    try {
      isLoading.value = true
      
      await api.post('/api/v1/auth/forgot-password', { email })
      
      return { success: true }
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const resetPassword = async ({ token: resetToken, new_password }) => {
    try {
      isLoading.value = true
      
      await api.post('/api/v1/auth/reset-password', {
        token: resetToken,
        new_password
      })
      
      return { success: true }
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const setupTokenRefresh = () => {
    // Clear any existing timer
    clearTokenRefresh()
    
    if (!isAuthenticated.value || isTokenExpired.value) return
    
    const expiresIn = tokenExpiresIn.value
    const refreshTime = Math.max(0, expiresIn - 5 * 60 * 1000) // Refresh 5 minutes before expiry
    
    if (refreshTime > 0) {
      refreshTimer.value = setTimeout(() => {
        refreshTokenAction().catch(() => {
          // Refresh failed, will be handled in refreshTokenAction
        })
      }, refreshTime)
    }
  }

  const clearTokenRefresh = () => {
    if (refreshTimer.value) {
      clearTimeout(refreshTimer.value)
      refreshTimer.value = null
    }
  }

  const checkAuth = async () => {
    if (!token.value) return false
    
    if (isTokenExpired.value) {
      try {
        await refreshTokenAction()
        return true
      } catch {
        return false
      }
    }
    
    // Fetch user data if not available
    if (!user.value) {
      try {
        await fetchUser()
      } catch {
        return false
      }
    }
    
    setupTokenRefresh()
    return true
  }

  // Initialize from localStorage
  const initializeAuth = () => {
    const storedToken = localStorage.getItem('auth_token')
    const storedRefreshToken = localStorage.getItem('refresh_token')
    const storedUser = localStorage.getItem('user')
    
    if (storedToken) {
      setToken(storedToken)
    }
    
    if (storedRefreshToken) {
      setRefreshToken(storedRefreshToken)
    }
    
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser))
      } catch (error) {
        console.error('Failed to parse stored user data:', error)
        localStorage.removeItem('user')
      }
    }
    
    // Check auth status if we have tokens
    if (storedToken && storedRefreshToken) {
      checkAuth()
    }
  }

  return {
    // State
    token,
    refreshToken,
    user,
    isLoading,
    redirectRoute,
    loginAttempts,
    lastLoginAttempt,
    
    // Getters
    isAuthenticated,
    userId,
    userName,
    userEmail,
    userRoles,
    userPermissions,
    hasRole,
    hasPermission,
    hasPermissions,
    hasAnyPermission,
    isTokenExpired,
    tokenExpiresIn,
    
    // Actions
    setToken,
    setRefreshToken,
    setUser,
    setRedirectRoute,
    clearRedirectRoute,
    incrementLoginAttempts,
    resetLoginAttempts,
    clearAuth,
    login,
    logout,
    refreshToken: refreshTokenAction,
    fetchUser,
    forgotPassword,
    resetPassword,
    setupTokenRefresh,
    clearTokenRefresh,
    checkAuth,
    initializeAuth
  }
})
