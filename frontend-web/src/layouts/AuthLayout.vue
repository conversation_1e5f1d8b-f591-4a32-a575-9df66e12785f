<template>
  <q-layout view="hHh lpR fFf" class="auth-layout-wrapper">
    <!-- Main Content -->
    <q-page-container>
      <router-view />
    </q-page-container>

    <!-- Global Loading -->
    <q-inner-loading
      :showing="isGlobalLoading"
      label="Loading..."
      label-class="text-white"
      label-style="font-size: 1.1em"
      color="white"
    />

    <!-- Accessibility Skip Link -->
    <a
      href="#main-content"
      class="skip-link"
      @click="skipToMain"
    >
      Skip to main content
    </a>
  </q-layout>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useAuthStore } from '@/stores/auth'
import { useLanguage } from '@/composables/useLanguage'

export default {
  name: 'AuthLayout',
  setup() {
    const $q = useQuasar()
    const authStore = useAuthStore()
    const { initializeLanguage, isRTL } = useLanguage()

    // Global loading state
    const isGlobalLoading = computed(() => authStore.isLoading)

    // Accessibility
    const skipToMain = () => {
      const mainContent = document.getElementById('main-content')
      if (mainContent) {
        mainContent.focus()
        mainContent.scrollIntoView()
      }
    }

    // Set up theme and accessibility
    const setupTheme = () => {
      // Set up dark mode based on user preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      $q.dark.set(prefersDark)

      // Set up RTL if needed
      if (isRTL.value) {
        document.documentElement.dir = 'rtl'
        $q.lang.rtl = true
      } else {
        document.documentElement.dir = 'ltr'
        $q.lang.rtl = false
      }

      // Set up high contrast mode
      const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches
      if (prefersHighContrast) {
        document.documentElement.classList.add('high-contrast')
      }

      // Set up reduced motion
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
      if (prefersReducedMotion) {
        document.documentElement.classList.add('reduced-motion')
      }
    }

    // Handle keyboard navigation
    const handleKeyboardNavigation = (event) => {
      // Tab trap for modal-like behavior if needed
      if (event.key === 'Tab') {
        const focusableElements = document.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        )
        const firstElement = focusableElements[0]
        const lastElement = focusableElements[focusableElements.length - 1]

        if (event.shiftKey && document.activeElement === firstElement) {
          event.preventDefault()
          lastElement.focus()
        } else if (!event.shiftKey && document.activeElement === lastElement) {
          event.preventDefault()
          firstElement.focus()
        }
      }

      // Escape key handling
      if (event.key === 'Escape') {
        // Close any open dialogs or modals
        const activeElement = document.activeElement
        if (activeElement && activeElement.blur) {
          activeElement.blur()
        }
      }
    }

    // Lifecycle
    onMounted(() => {
      // Initialize language
      initializeLanguage()
      
      // Set up theme and accessibility
      setupTheme()
      
      // Add keyboard event listeners
      document.addEventListener('keydown', handleKeyboardNavigation)
      
      // Set up media query listeners for theme changes
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)')
      darkModeQuery.addEventListener('change', (e) => {
        $q.dark.set(e.matches)
      })

      const highContrastQuery = window.matchMedia('(prefers-contrast: high)')
      highContrastQuery.addEventListener('change', (e) => {
        if (e.matches) {
          document.documentElement.classList.add('high-contrast')
        } else {
          document.documentElement.classList.remove('high-contrast')
        }
      })

      const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
      reducedMotionQuery.addEventListener('change', (e) => {
        if (e.matches) {
          document.documentElement.classList.add('reduced-motion')
        } else {
          document.documentElement.classList.remove('reduced-motion')
        }
      })
    })

    return {
      isGlobalLoading,
      skipToMain
    }
  }
}
</script>

<style lang="scss" scoped>
.auth-layout-wrapper {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  // Ensure full height
  .q-page-container {
    min-height: 100vh;
  }
}

// Skip link for accessibility
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  font-size: 14px;
  
  &:focus {
    top: 6px;
  }
}

// Global styles for auth layout
:deep(.q-page) {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 1rem;
}

// High contrast mode support
:global(.high-contrast) {
  .auth-layout-wrapper {
    background: #000;
    color: #fff;
  }
  
  :deep(.q-card) {
    border: 2px solid #fff;
    background: #000 !important;
    color: #fff !important;
  }
  
  :deep(.q-field__control) {
    border: 2px solid #fff !important;
    background: #000 !important;
    color: #fff !important;
  }
  
  :deep(.q-btn) {
    border: 2px solid #fff !important;
  }
}

// Reduced motion support
:global(.reduced-motion) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

// Dark mode support
.body--dark {
  .auth-layout-wrapper {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  }
}

// Mobile responsiveness
@media (max-width: 640px) {
  :deep(.q-page) {
    padding: 0.5rem;
  }
  
  .skip-link {
    left: 4px;
    
    &:focus {
      top: 4px;
    }
  }
}

// Print styles
@media print {
  .auth-layout-wrapper {
    background: white !important;
  }
  
  .skip-link {
    display: none;
  }
}

// Focus management
:deep(*:focus) {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

// Ensure proper contrast for focus indicators
:global(.high-contrast) {
  :deep(*:focus) {
    outline: 3px solid #fff;
    outline-offset: 2px;
  }
}
</style>
