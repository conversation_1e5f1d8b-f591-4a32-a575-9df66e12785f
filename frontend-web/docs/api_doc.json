{"openapi": "3.1.0", "info": {"title": "Smart Factory WMS", "description": "Smart Factory Warehouse Management System API", "version": "1.0.0"}, "paths": {"/health": {"get": {"summary": "Health Check", "description": "Health check endpoint for load balancers and monitoring.", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/": {"get": {"summary": "Root", "description": "Root endpoint with basic API information.", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/auth/login": {"post": {"tags": ["authentication"], "summary": "<PERSON><PERSON>", "description": "Authenticate user with username/email and password.\n\nThis endpoint supports authentication with either username or email address.\nThe system implements rate limiting and account locking for security.\n\nArgs:\n    request: Login credentials (username/email and password)\n    http_request: HTTP request object for audit logging\n    db: Database session\n\nReturns:\n    LoginResponse: Access token, refresh token, and user information\n\nRaises:\n    HTTPException:\n        - 401: Invalid credentials\n        - 404: User not found\n        - 423: Account locked\n        - 429: Too many requests (rate limit exceeded)\n        - 500: Server error\n\nHeaders:\n    X-Request-ID: Optional request ID for tracing", "operationId": "login_api_v1_auth_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/register": {"post": {"tags": ["authentication"], "summary": "Register", "description": "Register a new user account.\n\nArgs:\n    request: Registration data\n    http_request: HTTP request object for audit logging\n    db: Database session\n    \nReturns:\n    RegisterResponse: Registration confirmation\n    \nRaises:\n    HTTPException: If registration fails", "operationId": "register_api_v1_auth_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/refresh": {"post": {"tags": ["authentication"], "summary": "Refresh <PERSON>", "description": "Refresh access token using refresh token.\n\nArgs:\n    request: Refresh token request\n    db: Database session\n    \nReturns:\n    RefreshTokenResponse: New access token and optionally new refresh token\n    \nRaises:\n    HTTPException: If token refresh fails", "operationId": "refresh_token_api_v1_auth_refresh_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/logout": {"post": {"tags": ["authentication"], "summary": "Logout", "description": "Logout user and invalidate session.\n\nArgs:\n    http_request: HTTP request object\n    current_user_id: Current authenticated user ID\n    db: Database session\n    \nReturns:\n    Dict: Logout confirmation", "operationId": "logout_api_v1_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/auth/me": {"get": {"tags": ["authentication"], "summary": "Get Current User", "description": "Get current authenticated user information.\n\nArgs:\n    current_user_id: Current authenticated user ID\n    db: Database session\n    \nReturns:\n    UserResponse: Current user information", "operationId": "get_current_user_api_v1_auth_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}}}, "/api/v1/auth/change-password": {"post": {"tags": ["authentication"], "summary": "Change Password", "description": "Change user password.\n\nArgs:\n    request: Password change request\n    current_user_id: Current authenticated user ID\n    db: Database session\n    \nReturns:\n    Dict: Password change confirmation", "operationId": "change_password_api_v1_auth_change_password_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/forgot-password": {"post": {"tags": ["authentication"], "summary": "Forgot Password", "description": "Initiate password reset process.\n\nArgs:\n    request: Forgot password request\n    db: Database session\n    \nReturns:\n    Dict: Password reset initiation confirmation", "operationId": "forgot_password_api_v1_auth_forgot_password_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/reset-password": {"post": {"tags": ["authentication"], "summary": "Reset Password", "description": "Reset user password using reset token.\n\nArgs:\n    request: Password reset request\n    db: Database session\n    \nReturns:\n    Dict: Password reset confirmation", "operationId": "reset_password_api_v1_auth_reset_password_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/": {"get": {"tags": ["users"], "summary": "Get Users", "description": "Get list of users with optional filtering.", "operationId": "get_users_api_v1_users__get", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"type": "string", "title": "Search"}}, {"name": "is_active", "in": "query", "required": false, "schema": {"type": "boolean", "title": "Is Active"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserResponse"}, "title": "Response Get Users Api V1 Users  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{user_id}": {"get": {"tags": ["users"], "summary": "Get User", "description": "Get user by ID.", "operationId": "get_user_api_v1_users__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/inventory/": {"get": {"tags": ["inventory"], "summary": "Get Inventory", "description": "Get inventory list.", "operationId": "get_inventory_api_v1_inventory__get", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/inventory/{inventory_id}": {"get": {"tags": ["inventory"], "summary": "Get Inventory Item", "description": "Get inventory item by ID.", "operationId": "get_inventory_item_api_v1_inventory__inventory_id__get", "parameters": [{"name": "inventory_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Inventory Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/receiving/": {"get": {"tags": ["receiving"], "summary": "Get Receiving Orders", "description": "Get receiving orders list.", "operationId": "get_receiving_orders_api_v1_receiving__get", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/shipment/": {"get": {"tags": ["shipment"], "summary": "Get Shipment Orders", "description": "Get shipment orders list.", "operationId": "get_shipment_orders_api_v1_shipment__get", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/production/": {"get": {"tags": ["production"], "summary": "Get Production Orders", "description": "Get production orders list.", "operationId": "get_production_orders_api_v1_production__get", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"ChangePasswordRequest": {"properties": {"current_password": {"type": "string", "title": "Current Password", "description": "Current password"}, "new_password": {"type": "string", "minLength": 8, "title": "New Password", "description": "New password"}}, "type": "object", "required": ["current_password", "new_password"], "title": "ChangePasswordRequest", "description": "<PERSON>hema for password change request."}, "ForgotPasswordRequest": {"properties": {"email": {"type": "string", "format": "email", "title": "Email", "description": "User email address"}}, "type": "object", "required": ["email"], "title": "ForgotPasswordRequest", "description": "<PERSON><PERSON><PERSON> for forgot password request."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "LoginRequest": {"properties": {"username": {"type": "string", "maxLength": 50, "minLength": 3, "title": "Username", "description": "Username or email address"}, "password": {"type": "string", "maxLength": 128, "minLength": 8, "title": "Password", "description": "User password"}, "remember_me": {"type": "boolean", "title": "Remember Me", "description": "Remember user session", "default": false}}, "type": "object", "required": ["username", "password"], "title": "LoginRequest", "description": "Schema for user login request."}, "LoginResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token", "description": "JWT access token"}, "refresh_token": {"type": "string", "title": "Refresh <PERSON>", "description": "JWT refresh token"}, "token_type": {"type": "string", "title": "Token Type", "description": "Token type", "default": "bearer"}, "expires_in": {"type": "integer", "title": "Expires In", "description": "Token expiration time in seconds"}, "user": {"allOf": [{"$ref": "#/components/schemas/UserResponse"}], "description": "User information"}}, "type": "object", "required": ["access_token", "refresh_token", "expires_in", "user"], "title": "LoginResponse", "description": "Schema for login response."}, "RefreshTokenRequest": {"properties": {"refresh_token": {"type": "string", "title": "Refresh <PERSON>", "description": "Refresh token"}}, "type": "object", "required": ["refresh_token"], "title": "RefreshTokenRequest", "description": "Schema for token refresh request."}, "RefreshTokenResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token", "description": "New JWT access token"}, "refresh_token": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refresh <PERSON>", "description": "New JWT refresh token (if rotated)"}, "token_type": {"type": "string", "title": "Token Type", "description": "Token type", "default": "bearer"}, "expires_in": {"type": "integer", "title": "Expires In", "description": "Token expiration time in seconds"}}, "type": "object", "required": ["access_token", "expires_in"], "title": "RefreshTokenResponse", "description": "Schema for token refresh response."}, "RegisterRequest": {"properties": {"email": {"type": "string", "format": "email", "title": "Email", "description": "User email address"}, "username": {"type": "string", "maxLength": 50, "minLength": 3, "title": "Username", "description": "Username"}, "password": {"type": "string", "minLength": 8, "title": "Password", "description": "User password"}, "first_name": {"type": "string", "maxLength": 50, "minLength": 1, "title": "First Name", "description": "First name"}, "last_name": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Last Name", "description": "Last name"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "Phone number"}, "language": {"anyOf": [{"type": "string", "maxLength": 5}, {"type": "null"}], "title": "Language", "description": "Preferred language", "default": "en"}, "timezone": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Timezone", "description": "User timezone", "default": "UTC"}}, "type": "object", "required": ["email", "username", "password", "first_name", "last_name"], "title": "RegisterRequest", "description": "Schema for user registration request."}, "RegisterResponse": {"properties": {"message": {"type": "string", "title": "Message", "description": "Registration confirmation message"}, "user": {"allOf": [{"$ref": "#/components/schemas/UserResponse"}], "description": "Created user information"}}, "type": "object", "required": ["message", "user"], "title": "RegisterResponse", "description": "Schema for registration response."}, "ResetPasswordRequest": {"properties": {"token": {"type": "string", "title": "Token", "description": "Password reset token"}, "new_password": {"type": "string", "minLength": 8, "title": "New Password", "description": "New password"}}, "type": "object", "required": ["token", "new_password"], "title": "ResetPasswordRequest", "description": "<PERSON><PERSON>a for password reset request."}, "RoleResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "display_name": {"type": "string", "title": "Display Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "is_active": {"type": "boolean", "title": "Is Active"}}, "type": "object", "required": ["id", "name", "display_name", "is_active"], "title": "RoleResponse", "description": "<PERSON><PERSON><PERSON> for role response."}, "UserResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "email": {"type": "string", "title": "Email"}, "username": {"type": "string", "title": "Username"}, "first_name": {"type": "string", "title": "First Name"}, "last_name": {"type": "string", "title": "Last Name"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "avatar_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatar Url"}, "is_active": {"type": "boolean", "title": "Is Active"}, "is_verified": {"type": "boolean", "title": "Is Verified"}, "language": {"type": "string", "title": "Language"}, "timezone": {"type": "string", "title": "Timezone"}, "last_login_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "roles": {"items": {"$ref": "#/components/schemas/RoleResponse"}, "type": "array", "title": "Roles", "default": []}}, "type": "object", "required": ["id", "email", "username", "first_name", "last_name", "is_active", "is_verified", "language", "timezone", "created_at"], "title": "UserResponse", "description": "Schema for user response."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}