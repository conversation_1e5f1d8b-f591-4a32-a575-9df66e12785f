/**
 * Smart Factory WMS - Authentication Store Tests
 * 
 * Unit tests for the Pinia authentication store.
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useAuthStore } from '@/stores/auth'
import api from '@/services/api'

// Mock the API service
vi.mock('@/services/api', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn(),
    defaults: {
      headers: {
        common: {}
      }
    }
  }
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock crypto.randomUUID
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: vi.fn(() => 'test-uuid-123')
  }
})

describe('Auth Store', () => {
  let authStore

  beforeEach(() => {
    setActivePinia(createPinia())
    authStore = useAuthStore()
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      expect(authStore.token).toBeNull()
      expect(authStore.refreshToken).toBeNull()
      expect(authStore.user).toBeNull()
      expect(authStore.isLoading).toBe(false)
      expect(authStore.redirectRoute).toBeNull()
      expect(authStore.loginAttempts).toBe(0)
      expect(authStore.lastLoginAttempt).toBeNull()
    })

    it('should not be authenticated initially', () => {
      expect(authStore.isAuthenticated).toBe(false)
    })
  })

  describe('Getters', () => {
    beforeEach(() => {
      authStore.user = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        roles: [
          {
            name: 'admin',
            permissions: [
              { name: 'users.create' },
              { name: 'users.read' }
            ]
          }
        ]
      }
      authStore.token = 'valid-token'
    })

    it('should return correct user info', () => {
      expect(authStore.userId).toBe(1)
      expect(authStore.userName).toBe('testuser')
      expect(authStore.userEmail).toBe('<EMAIL>')
    })

    it('should return user roles', () => {
      expect(authStore.userRoles).toHaveLength(1)
      expect(authStore.userRoles[0].name).toBe('admin')
    })

    it('should return user permissions', () => {
      expect(authStore.userPermissions).toHaveLength(2)
      expect(authStore.userPermissions[0].name).toBe('users.create')
      expect(authStore.userPermissions[1].name).toBe('users.read')
    })

    it('should check roles correctly', () => {
      expect(authStore.hasRole('admin')).toBe(true)
      expect(authStore.hasRole('user')).toBe(false)
    })

    it('should check permissions correctly', () => {
      expect(authStore.hasPermission('users.create')).toBe(true)
      expect(authStore.hasPermission('users.delete')).toBe(false)
    })

    it('should check multiple permissions correctly', () => {
      expect(authStore.hasPermissions(['users.create', 'users.read'])).toBe(true)
      expect(authStore.hasPermissions(['users.create', 'users.delete'])).toBe(false)
    })

    it('should check any permission correctly', () => {
      expect(authStore.hasAnyPermission(['users.create', 'users.delete'])).toBe(true)
      expect(authStore.hasAnyPermission(['users.delete', 'posts.create'])).toBe(false)
    })
  })

  describe('Actions', () => {
    describe('login', () => {
      const loginData = {
        username: 'testuser',
        password: 'password123',
        remember_me: true
      }

      const mockResponse = {
        data: {
          access_token: 'access-token-123',
          refresh_token: 'refresh-token-123',
          user: {
            id: 1,
            username: 'testuser',
            email: '<EMAIL>'
          }
        }
      }

      it('should login successfully', async () => {
        api.post.mockResolvedValue(mockResponse)

        const result = await authStore.login(loginData)

        expect(api.post).toHaveBeenCalledWith('/api/v1/auth/login', {
          username: 'testuser',
          password: 'password123',
          remember_me: true
        }, {
          headers: {
            'X-Request-ID': 'test-uuid-123'
          }
        })

        expect(result.success).toBe(true)
        expect(result.user).toEqual(mockResponse.data.user)
        expect(authStore.token).toBe('access-token-123')
        expect(authStore.refreshToken).toBe('refresh-token-123')
        expect(authStore.user).toEqual(mockResponse.data.user)
        expect(authStore.loginAttempts).toBe(0)
      })

      it('should handle login failure', async () => {
        const error = new Error('Invalid credentials')
        error.response = { status: 401 }
        api.post.mockRejectedValue(error)

        await expect(authStore.login(loginData)).rejects.toThrow('Invalid credentials')
        expect(authStore.loginAttempts).toBe(1)
        expect(authStore.token).toBeNull()
        expect(authStore.user).toBeNull()
      })

      it('should set loading state during login', async () => {
        api.post.mockImplementation(() => {
          expect(authStore.isLoading).toBe(true)
          return Promise.resolve(mockResponse)
        })

        await authStore.login(loginData)
        expect(authStore.isLoading).toBe(false)
      })
    })

    describe('logout', () => {
      beforeEach(() => {
        authStore.token = 'test-token'
        authStore.refreshToken = 'test-refresh-token'
        authStore.user = { id: 1, username: 'testuser' }
      })

      it('should logout successfully', async () => {
        api.post.mockResolvedValue({})

        await authStore.logout()

        expect(api.post).toHaveBeenCalledWith('/api/v1/auth/logout')
        expect(authStore.token).toBeNull()
        expect(authStore.refreshToken).toBeNull()
        expect(authStore.user).toBeNull()
        expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
        expect(localStorageMock.removeItem).toHaveBeenCalledWith('refresh_token')
        expect(localStorageMock.removeItem).toHaveBeenCalledWith('user')
      })

      it('should clear auth even if API call fails', async () => {
        api.post.mockRejectedValue(new Error('Network error'))

        await authStore.logout()

        expect(authStore.token).toBeNull()
        expect(authStore.refreshToken).toBeNull()
        expect(authStore.user).toBeNull()
      })
    })

    describe('forgotPassword', () => {
      it('should send forgot password request', async () => {
        api.post.mockResolvedValue({})

        const result = await authStore.forgotPassword('<EMAIL>')

        expect(api.post).toHaveBeenCalledWith('/api/v1/auth/forgot-password', {
          email: '<EMAIL>'
        })
        expect(result.success).toBe(true)
      })

      it('should handle forgot password error', async () => {
        const error = new Error('User not found')
        api.post.mockRejectedValue(error)

        await expect(authStore.forgotPassword('<EMAIL>')).rejects.toThrow('User not found')
      })
    })

    describe('resetPassword', () => {
      it('should reset password successfully', async () => {
        api.post.mockResolvedValue({})

        const result = await authStore.resetPassword({
          token: 'reset-token',
          new_password: 'newpassword123'
        })

        expect(api.post).toHaveBeenCalledWith('/api/v1/auth/reset-password', {
          token: 'reset-token',
          new_password: 'newpassword123'
        })
        expect(result.success).toBe(true)
      })
    })
  })

  describe('Token Management', () => {
    it('should set token and update API headers', () => {
      authStore.setToken('test-token')

      expect(authStore.token).toBe('test-token')
      expect(api.defaults.headers.common['Authorization']).toBe('Bearer test-token')
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', 'test-token')
    })

    it('should clear token and remove API headers', () => {
      authStore.setToken('test-token')
      authStore.setToken(null)

      expect(authStore.token).toBeNull()
      expect(api.defaults.headers.common['Authorization']).toBeUndefined()
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
    })

    it('should set refresh token', () => {
      authStore.setRefreshToken('refresh-token')

      expect(authStore.refreshToken).toBe('refresh-token')
      expect(localStorageMock.setItem).toHaveBeenCalledWith('refresh_token', 'refresh-token')
    })
  })

  describe('Initialization', () => {
    it('should initialize from localStorage', () => {
      localStorageMock.getItem.mockImplementation((key) => {
        switch (key) {
          case 'auth_token':
            return 'stored-token'
          case 'refresh_token':
            return 'stored-refresh-token'
          case 'user':
            return JSON.stringify({ id: 1, username: 'testuser' })
          default:
            return null
        }
      })

      authStore.initializeAuth()

      expect(authStore.token).toBe('stored-token')
      expect(authStore.refreshToken).toBe('stored-refresh-token')
      expect(authStore.user).toEqual({ id: 1, username: 'testuser' })
    })

    it('should handle invalid JSON in localStorage', () => {
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'user') return 'invalid-json'
        return null
      })

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      authStore.initializeAuth()

      expect(authStore.user).toBeNull()
      expect(consoleSpy).toHaveBeenCalled()
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('user')

      consoleSpy.mockRestore()
    })
  })
})
