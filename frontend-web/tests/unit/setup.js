/**
 * Smart Factory WMS - Test Setup
 * 
 * Global test setup and configuration for unit tests.
 */

import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// Mock global objects
global.ResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
})

// Mock crypto.randomUUID
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: vi.fn(() => 'test-uuid-123')
  }
})

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
}

// Global test utilities
export const createMockRouter = () => ({
  push: vi.fn(),
  replace: vi.fn(),
  go: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  currentRoute: {
    value: {
      path: '/',
      name: 'Home',
      params: {},
      query: {},
      meta: {}
    }
  }
})

export const createMockI18n = () => ({
  t: vi.fn((key, params) => {
    if (params) {
      return key.replace(/\{(\w+)\}/g, (match, param) => params[param] || match)
    }
    return key
  }),
  locale: { value: 'en' },
  availableLocales: ['en', 'ja', 'zh', 'vi']
})

export const createMockQuasar = () => ({
  notify: vi.fn(),
  loading: {
    show: vi.fn(),
    hide: vi.fn()
  },
  dialog: vi.fn(),
  dark: {
    isActive: false,
    set: vi.fn(),
    toggle: vi.fn()
  },
  lang: {
    rtl: false
  }
})

// Configure Vue Test Utils
config.global.mocks = {
  $t: (key, params) => {
    if (params) {
      return key.replace(/\{(\w+)\}/g, (match, param) => params[param] || match)
    }
    return key
  },
  $router: createMockRouter(),
  $route: {
    path: '/',
    name: 'Home',
    params: {},
    query: {},
    meta: {}
  },
  $q: createMockQuasar()
}

// Global stubs for common components
config.global.stubs = {
  'router-link': true,
  'router-view': true,
  'transition': false,
  'transition-group': false
}

// Reset all mocks before each test
beforeEach(() => {
  vi.clearAllMocks()
  localStorageMock.getItem.mockReturnValue(null)
  sessionStorageMock.getItem.mockReturnValue(null)
})
