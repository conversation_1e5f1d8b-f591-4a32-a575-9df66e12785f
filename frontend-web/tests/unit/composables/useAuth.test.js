/**
 * Smart Factory WMS - useAuth Composable Tests
 * 
 * Unit tests for the authentication composable.
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useAuth } from '@/composables/useAuth'
import { useAuthStore } from '@/stores/auth'

// Mock dependencies
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: vi.fn()
  })
}))

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: vi.fn((key) => key)
  })
}))

vi.mock('quasar', () => ({
  useQuasar: () => ({
    notify: vi.fn()
  })
}))

vi.mock('@/stores/auth')

describe('useAuth Composable', () => {
  let authComposable
  let mockAuthStore
  let mockRouter
  let mockQuasar

  beforeEach(() => {
    setActivePinia(createPinia())
    
    // Mock auth store
    mockAuthStore = {
      isAuthenticated: false,
      isLoading: false,
      loginAttempts: 0,
      lastLoginAttempt: null,
      login: vi.fn(),
      logout: vi.fn(),
      forgotPassword: vi.fn(),
      resetPassword: vi.fn(),
      setRedirectRoute: vi.fn(),
      clearRedirectRoute: vi.fn()
    }
    
    useAuthStore.mockReturnValue(mockAuthStore)
    
    authComposable = useAuth()
    mockRouter = authComposable.authStore
    mockQuasar = { notify: vi.fn() }
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Form State', () => {
    it('should initialize with empty form', () => {
      expect(authComposable.loginForm.username).toBe('')
      expect(authComposable.loginForm.password).toBe('')
      expect(authComposable.loginForm.remember_me).toBe(false)
    })

    it('should initialize UI state correctly', () => {
      expect(authComposable.showPassword.value).toBe(false)
      expect(authComposable.isSubmitting.value).toBe(false)
      expect(authComposable.errors.value).toEqual({})
    })
  })

  describe('Validation', () => {
    it('should validate username correctly', () => {
      // Test empty username
      expect(authComposable.validateField('username', '')).toBe(false)
      expect(authComposable.errors.value.username).toBe('auth.validation.username_required')

      // Test short username
      expect(authComposable.validateField('username', 'ab')).toBe(false)
      expect(authComposable.errors.value.username).toBe('auth.validation.username_min_length')

      // Test long username
      const longUsername = 'a'.repeat(51)
      expect(authComposable.validateField('username', longUsername)).toBe(false)
      expect(authComposable.errors.value.username).toBe('auth.validation.username_max_length')

      // Test valid username
      expect(authComposable.validateField('username', 'validuser')).toBe(true)
      expect(authComposable.errors.value.username).toBeUndefined()

      // Test valid email
      expect(authComposable.validateField('username', '<EMAIL>')).toBe(true)
      expect(authComposable.errors.value.username).toBeUndefined()
    })

    it('should validate password correctly', () => {
      // Test empty password
      expect(authComposable.validateField('password', '')).toBe(false)
      expect(authComposable.errors.value.password).toBe('auth.validation.password_required')

      // Test short password
      expect(authComposable.validateField('password', '1234567')).toBe(false)
      expect(authComposable.errors.value.password).toBe('auth.validation.password_min_length')

      // Test long password
      const longPassword = 'a'.repeat(129)
      expect(authComposable.validateField('password', longPassword)).toBe(false)
      expect(authComposable.errors.value.password).toBe('auth.validation.password_max_length')

      // Test valid password
      expect(authComposable.validateField('password', 'validpassword123')).toBe(true)
      expect(authComposable.errors.value.password).toBeUndefined()
    })

    it('should validate email correctly', () => {
      // Test invalid email
      expect(authComposable.validateField('email', 'invalid-email')).toBe(false)
      expect(authComposable.errors.value.email).toBe('form.invalid_email')

      // Test valid email
      expect(authComposable.validateField('email', '<EMAIL>')).toBe(true)
      expect(authComposable.errors.value.email).toBeUndefined()
    })

    it('should validate form correctly', () => {
      // Test invalid form
      authComposable.loginForm.username = ''
      authComposable.loginForm.password = ''
      expect(authComposable.validateForm()).toBe(false)

      // Test valid form
      authComposable.loginForm.username = 'testuser'
      authComposable.loginForm.password = 'password123'
      expect(authComposable.validateForm()).toBe(true)
    })
  })

  describe('Computed Properties', () => {
    it('should compute form validity correctly', () => {
      // Invalid form
      authComposable.loginForm.username = ''
      authComposable.loginForm.password = ''
      expect(authComposable.isFormValid.value).toBe(false)

      // Valid form
      authComposable.loginForm.username = 'testuser'
      authComposable.loginForm.password = 'password123'
      expect(authComposable.isFormValid.value).toBe(true)
    })

    it('should compute can submit correctly', () => {
      authComposable.loginForm.username = 'testuser'
      authComposable.loginForm.password = 'password123'
      authComposable.isSubmitting.value = false
      mockAuthStore.isLoading = false

      expect(authComposable.canSubmit.value).toBe(true)

      // Test with submitting
      authComposable.isSubmitting.value = true
      expect(authComposable.canSubmit.value).toBe(false)

      // Test with store loading
      authComposable.isSubmitting.value = false
      mockAuthStore.isLoading = true
      expect(authComposable.canSubmit.value).toBe(false)
    })

    it('should compute login attempts exceeded correctly', () => {
      mockAuthStore.loginAttempts = 3
      expect(authComposable.loginAttemptsExceeded.value).toBe(false)

      mockAuthStore.loginAttempts = 5
      expect(authComposable.loginAttemptsExceeded.value).toBe(true)
    })
  })

  describe('Actions', () => {
    describe('handleLogin', () => {
      beforeEach(() => {
        authComposable.loginForm.username = 'testuser'
        authComposable.loginForm.password = 'password123'
        authComposable.loginForm.remember_me = true
      })

      it('should handle successful login', async () => {
        mockAuthStore.login.mockResolvedValue({
          success: true,
          user: { id: 1, username: 'testuser' }
        })

        await authComposable.handleLogin()

        expect(mockAuthStore.login).toHaveBeenCalledWith({
          username: 'testuser',
          password: 'password123',
          remember_me: true
        })
      })

      it('should handle login validation failure', async () => {
        authComposable.loginForm.username = ''
        authComposable.loginForm.password = ''

        await authComposable.handleLogin()

        expect(mockAuthStore.login).not.toHaveBeenCalled()
      })

      it('should handle rate limiting', async () => {
        mockAuthStore.loginAttempts = 5
        mockAuthStore.lastLoginAttempt = Date.now()

        await authComposable.handleLogin()

        expect(mockAuthStore.login).not.toHaveBeenCalled()
      })

      it('should handle login error', async () => {
        const error = new Error('Invalid credentials')
        error.response = { status: 401 }
        mockAuthStore.login.mockRejectedValue(error)

        await authComposable.handleLogin()

        expect(authComposable.isSubmitting.value).toBe(false)
      })
    })

    describe('handleForgotPassword', () => {
      it('should handle successful forgot password', async () => {
        authComposable.forgotPasswordForm.email = '<EMAIL>'
        mockAuthStore.forgotPassword.mockResolvedValue({ success: true })

        await authComposable.handleForgotPassword()

        expect(mockAuthStore.forgotPassword).toHaveBeenCalledWith('<EMAIL>')
        expect(authComposable.forgotPasswordForm.email).toBe('')
      })

      it('should handle forgot password validation failure', async () => {
        authComposable.forgotPasswordForm.email = 'invalid-email'

        await authComposable.handleForgotPassword()

        expect(mockAuthStore.forgotPassword).not.toHaveBeenCalled()
      })
    })

    describe('handleResetPassword', () => {
      beforeEach(() => {
        authComposable.resetPasswordForm.token = 'reset-token'
        authComposable.resetPasswordForm.new_password = 'newpassword123'
        authComposable.resetPasswordForm.confirm_password = 'newpassword123'
      })

      it('should handle successful password reset', async () => {
        mockAuthStore.resetPassword.mockResolvedValue({ success: true })

        await authComposable.handleResetPassword()

        expect(mockAuthStore.resetPassword).toHaveBeenCalledWith({
          token: 'reset-token',
          new_password: 'newpassword123'
        })
      })

      it('should handle password mismatch', async () => {
        authComposable.resetPasswordForm.confirm_password = 'different-password'

        await authComposable.handleResetPassword()

        expect(mockAuthStore.resetPassword).not.toHaveBeenCalled()
      })
    })

    describe('logout', () => {
      it('should handle successful logout', async () => {
        mockAuthStore.logout.mockResolvedValue()

        await authComposable.logout()

        expect(mockAuthStore.logout).toHaveBeenCalled()
      })

      it('should handle logout error', async () => {
        mockAuthStore.logout.mockRejectedValue(new Error('Network error'))
        mockAuthStore.clearAuth = vi.fn()

        await authComposable.logout()

        expect(mockAuthStore.clearAuth).toHaveBeenCalled()
      })
    })
  })

  describe('Utility Functions', () => {
    it('should toggle password visibility', () => {
      expect(authComposable.showPassword.value).toBe(false)
      
      authComposable.togglePasswordVisibility()
      expect(authComposable.showPassword.value).toBe(true)
      
      authComposable.togglePasswordVisibility()
      expect(authComposable.showPassword.value).toBe(false)
    })

    it('should reset form', () => {
      authComposable.loginForm.username = 'testuser'
      authComposable.loginForm.password = 'password123'
      authComposable.loginForm.remember_me = true
      authComposable.errors.value = { username: 'error' }

      authComposable.resetForm()

      expect(authComposable.loginForm.username).toBe('')
      expect(authComposable.loginForm.password).toBe('')
      expect(authComposable.loginForm.remember_me).toBe(false)
      expect(authComposable.errors.value).toEqual({})
    })
  })
})
